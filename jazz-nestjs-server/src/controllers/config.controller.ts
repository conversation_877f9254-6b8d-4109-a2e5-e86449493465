import { Controller, Get, Post, Body, Param, Delete, Put } from '@nestjs/common';
import { ConfigService } from '../services/config.service';

@Controller('api/config')
export class ConfigController {
  constructor(private readonly configService: ConfigService) {}

  @Get()
  async getAllConfigs() {
    return await this.configService.getAllConfigs();
  }

  @Get(':key')
  async getConfig(@Param('key') key: string) {
    return await this.configService.getConfig(key);
  }

  @Post()
  async createConfig(@Body() body: { key: string; value: any }) {
    return await this.configService.setConfig(body.key, body.value);
  }

  @Put(':key')
  async updateConfig(@Param('key') key: string, @Body() body: { value: any }) {
    return await this.configService.setConfig(key, body.value);
  }

  @Delete(':key')
  async deleteConfig(@Param('key') key: string) {
    return await this.configService.deleteConfig(key);
  }

  @Get('llm/models')
  async getLLMModels() {
    return await this.configService.getLLMModels();
  }

  @Post('llm/models')
  async addLLMModel(@Body() model: any) {
    return await this.configService.addLLMModel(model);
  }

  @Delete('llm/models/:id')
  async deleteLLMModel(@Param('id') id: string) {
    return await this.configService.deleteLLMModel(id);
  }
}
