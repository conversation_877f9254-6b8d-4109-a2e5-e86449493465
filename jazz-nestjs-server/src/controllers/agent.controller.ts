import { Controller, Get, Post, Body, Param, Delete, Put } from '@nestjs/common';
import { ConfigService } from '../services/config.service';
import { WebSocketGateway } from '../gateways/websocket.gateway';

@Controller('api/agent')
export class AgentController {
  constructor(private readonly configService: ConfigService) {}

  @Get()
  async getAllAgents() {
    return await this.configService.getAgents();
  }

  @Get(':id')
  async getAgent(@Param('id') id: string) {
    return await this.configService.getAgent(id);
  }

  @Post()
  async createAgent(@Body() agent: any) {
    return await this.configService.createAgent(agent);
  }

  @Put(':id')
  async updateAgent(@Param('id') id: string, @Body() agent: any) {
    return await this.configService.updateAgent(id, agent);
  }

  @Delete(':id')
  async deleteAgent(@Param('id') id: string) {
    return await this.configService.deleteAgent(id);
  }

  @Post(':id/duplicate')
  async duplicateAgent(@Param('id') id: string) {
    return await this.configService.duplicateAgent(id);
  }

  @Get(':id/conversations')
  async getAgentConversations(@Param('id') id: string) {
    return await this.configService.getAgentConversations(id);
  }

  @Post(':id/conversations')
  async createConversation(@Param('id') id: string, @Body() conversation: any) {
    return await this.configService.createConversation(id, conversation);
  }

  @Delete(':id/conversations/:conversationId')
  async deleteConversation(
    @Param('id') id: string,
    @Param('conversationId') conversationId: string,
  ) {
    return await this.configService.deleteConversation(id, conversationId);
  }
}

// 添加模型列表端点到根路径
@Controller('api')
export class ModelController {
  constructor(
    private readonly configService: ConfigService,
    private readonly websocketGateway: WebSocketGateway
  ) {}

  @Get('list_models')
  async getModels() {
    return await this.configService.getModelList();
  }

  @Get('chat_session/:sessionId')
  async getChatSession(@Param('sessionId') sessionId: string) {
    return await this.configService.getChatSession(sessionId);
  }

  @Get('list_chat_sessions')
  async listChatSessions() {
    return await this.configService.listChatSessions();
  }

  @Post('chat')
  async handleChat(@Body() data: any) {
    try {
      // Extract data from request
      const { messages, session_id, canvas_id, text_model, image_model, system_prompt } = data;

      console.log('Received chat request:', {
        session_id,
        canvas_id,
        messages_count: messages?.length,
        text_model: text_model?.model,
        image_model: image_model?.model
      });

      // If there is only one message, create a new chat session
      if (messages && messages.length === 1) {
        const prompt = messages[0]?.content || '';
        await this.configService.createChatSession({
          id: session_id,
          model: text_model?.model || 'gpt-3.5-turbo',
          provider: text_model?.provider || 'openai',
          canvas_id,
          title: prompt.substring(0, 200)
        });
      }

      // Save user message
      if (messages && messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        await this.configService.createChatMessage({
          session_id,
          role: lastMessage.role || 'user',
          content: JSON.stringify(lastMessage)
        });
      }

      // Start chat processing asynchronously
      this.processChatAsync(data);

      return { status: 'done' };
    } catch (error) {
      console.error('Error in handleChat:', error);
      return { status: 'error', message: error.message };
    }
  }

  private async processChatAsync(data: any) {
    const { messages, session_id, canvas_id, text_model, image_model, system_prompt } = data;

    try {

      // Create chat session if this is the first message
      if (messages && messages.length === 1) {
        const prompt = messages[0]?.content || '';
        const title = typeof prompt === 'string' ? prompt.substring(0, 200) : 'New Chat';

        await this.configService.createChatSession({
          id: session_id,
          model: text_model?.model || 'gpt-3.5-turbo',
          provider: text_model?.provider || 'openai',
          canvas_id: canvas_id,
          title: title
        });
      }

      // Save user message
      if (messages && messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        await this.configService.createChatMessage({
          session_id,
          role: lastMessage.role || 'user',
          content: JSON.stringify(lastMessage)
        });
      }

      // Process with real AI
      await this.processWithAI(messages, session_id, canvas_id, text_model, image_model, system_prompt);

      console.log('Chat processing completed for session:', session_id);

    } catch (error) {
      console.error('Error in processChatAsync:', error);
    } finally {
      // Notify frontend that processing is done
      this.websocketGateway.broadcastToSession(session_id, {
        type: 'done'
      });
    }
  }

  private async processWithAI(messages: any[], session_id: string, canvas_id: string, text_model: any, image_model: any, system_prompt?: string) {
    try {
      // Get provider configuration
      const providers = await this.configService.getProviders();
      const provider = text_model?.provider || 'openai';
      const model = text_model?.model || 'gpt-3.5-turbo';
      const providerConfig = providers[provider];

      if (!providerConfig) {
        throw new Error(`Provider ${provider} not configured`);
      }

      // Prepare messages for AI
      const aiMessages = [...messages];
      if (system_prompt) {
        aiMessages.unshift({
          role: 'system',
          content: system_prompt
        });
      }

      // Call AI API based on provider
      let response = '';
      if (provider === 'openai' || provider === 'jaaz' || provider === '深度求索') {
        response = await this.callOpenAICompatibleAPI(providerConfig, model, aiMessages, session_id);
      } else if (provider === 'anthropic') {
        response = await this.callAnthropicAPI(providerConfig, model, aiMessages, session_id);
      } else {
        throw new Error(`Unsupported provider: ${provider}`);
      }

      // Save AI response
      await this.configService.createChatMessage({
        session_id,
        role: 'assistant',
        content: JSON.stringify({
          role: 'assistant',
          content: response
        })
      });

      // Broadcast final message update
      this.websocketGateway.broadcastToSession(session_id, {
        type: 'message_complete',
        message: {
          role: 'assistant',
          content: response
        }
      });

    } catch (error) {
      console.error('Error in processWithAI:', error);

      // Send error message to frontend
      this.websocketGateway.broadcastToSession(session_id, {
        type: 'error',
        message: `AI processing failed: ${error.message}`
      });
    }
  }

  private async callOpenAICompatibleAPI(providerConfig: any, model: string, messages: any[], session_id: string): Promise<string> {
    const url = providerConfig.url;
    const apiKey = providerConfig.api_key;
    const maxTokens = providerConfig.max_tokens || 4096;

    // If no API key, use mock response for testing
    if (!apiKey || apiKey.trim() === '') {
      const userMessage = messages[messages.length - 1]?.content || '';
      const mockResponse = `I understand you want me to work on: "${userMessage}". This is a mock response because no API key is configured for this provider. To get real AI responses, please configure your API key in the settings.`;

      // Simulate streaming
      for (let i = 0; i < mockResponse.length; i += 10) {
        const chunk = mockResponse.slice(i, i + 10);
        this.websocketGateway.broadcastToSession(session_id, {
          type: 'delta',
          text: chunk
        });
        await new Promise(resolve => setTimeout(resolve, 50)); // Small delay to simulate streaming
      }

      return mockResponse;
    }

    const response = await fetch(`${url}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: messages,
        max_tokens: maxTokens,
        temperature: 0.7,
        stream: true
      })
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    // Handle streaming response
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';

    if (reader) {
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') continue;

              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content;
                if (content) {
                  fullResponse += content;

                  // Broadcast streaming content
                  this.websocketGateway.broadcastToSession(session_id, {
                    type: 'delta',
                    text: content
                  });
                }
              } catch (e) {
                // Skip invalid JSON
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    }

    return fullResponse;
  }

  private async callAnthropicAPI(providerConfig: any, model: string, messages: any[], session_id: string): Promise<string> {
    // For now, return a placeholder - Anthropic API implementation would go here
    const userMessage = messages[messages.length - 1]?.content || '';
    const response = `[Anthropic API] I received your message: "${userMessage}". This is a placeholder response. The actual Anthropic API integration would be implemented here.`;

    // Simulate streaming
    for (let i = 0; i < response.length; i += 10) {
      const chunk = response.slice(i, i + 10);
      this.websocketGateway.broadcastToSession(session_id, {
        type: 'delta',
        text: chunk
      });
      await new Promise(resolve => setTimeout(resolve, 50)); // Small delay to simulate streaming
    }

    return response;
  }

  @Get('providers')
  async getProviders() {
    return await this.configService.getProviders();
  }
}
