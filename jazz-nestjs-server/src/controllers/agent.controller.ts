import { Controller, Get, Post, Body, Param, Delete, Put } from '@nestjs/common';
import { ConfigService } from '../services/config.service';

@Controller('api/agent')
export class AgentController {
  constructor(private readonly configService: ConfigService) {}

  @Get()
  async getAllAgents() {
    return await this.configService.getAgents();
  }

  @Get(':id')
  async getAgent(@Param('id') id: string) {
    return await this.configService.getAgent(id);
  }

  @Post()
  async createAgent(@Body() agent: any) {
    return await this.configService.createAgent(agent);
  }

  @Put(':id')
  async updateAgent(@Param('id') id: string, @Body() agent: any) {
    return await this.configService.updateAgent(id, agent);
  }

  @Delete(':id')
  async deleteAgent(@Param('id') id: string) {
    return await this.configService.deleteAgent(id);
  }

  @Post(':id/duplicate')
  async duplicateAgent(@Param('id') id: string) {
    return await this.configService.duplicateAgent(id);
  }

  @Get(':id/conversations')
  async getAgentConversations(@Param('id') id: string) {
    return await this.configService.getAgentConversations(id);
  }

  @Post(':id/conversations')
  async createConversation(@Param('id') id: string, @Body() conversation: any) {
    return await this.configService.createConversation(id, conversation);
  }

  @Delete(':id/conversations/:conversationId')
  async deleteConversation(
    @Param('id') id: string,
    @Param('conversationId') conversationId: string,
  ) {
    return await this.configService.deleteConversation(id, conversationId);
  }
}
