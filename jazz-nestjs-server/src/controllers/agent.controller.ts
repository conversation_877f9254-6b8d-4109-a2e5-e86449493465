import { Controller, Get, Post, Body, Param, Delete, Put } from '@nestjs/common';
import { ConfigService } from '../services/config.service';

@Controller('api/agent')
export class AgentController {
  constructor(private readonly configService: ConfigService) {}

  @Get()
  async getAllAgents() {
    return await this.configService.getAgents();
  }

  @Get(':id')
  async getAgent(@Param('id') id: string) {
    return await this.configService.getAgent(id);
  }

  @Post()
  async createAgent(@Body() agent: any) {
    return await this.configService.createAgent(agent);
  }

  @Put(':id')
  async updateAgent(@Param('id') id: string, @Body() agent: any) {
    return await this.configService.updateAgent(id, agent);
  }

  @Delete(':id')
  async deleteAgent(@Param('id') id: string) {
    return await this.configService.deleteAgent(id);
  }

  @Post(':id/duplicate')
  async duplicateAgent(@Param('id') id: string) {
    return await this.configService.duplicateAgent(id);
  }

  @Get(':id/conversations')
  async getAgentConversations(@Param('id') id: string) {
    return await this.configService.getAgentConversations(id);
  }

  @Post(':id/conversations')
  async createConversation(@Param('id') id: string, @Body() conversation: any) {
    return await this.configService.createConversation(id, conversation);
  }

  @Delete(':id/conversations/:conversationId')
  async deleteConversation(
    @Param('id') id: string,
    @Param('conversationId') conversationId: string,
  ) {
    return await this.configService.deleteConversation(id, conversationId);
  }
}

// 添加模型列表端点到根路径
@Controller('api')
export class ModelController {
  constructor(private readonly configService: ConfigService) {}

  @Get('list_models')
  async getModels() {
    return await this.configService.getModelList();
  }

  @Get('chat_session/:sessionId')
  async getChatSession(@Param('sessionId') sessionId: string) {
    return await this.configService.getChatSession(sessionId);
  }

  @Get('list_chat_sessions')
  async listChatSessions() {
    return await this.configService.listChatSessions();
  }

  @Post('chat')
  async handleChat(@Body() data: any) {
    // Handle chat request - for now just return success
    // In a full implementation, this would process the chat and return messages
    return { status: 'done' };
  }
}
