import { Injectable } from '@nestjs/common';
import { DatabaseService } from './database.service';
import { nanoid } from 'nanoid';

@Injectable()
export class ConfigService {
  constructor(private readonly databaseService: DatabaseService) {}

  // Config operations
  async getAllConfigs() {
    return await this.databaseService.getAllConfigs();
  }

  async getConfig(key: string) {
    return await this.databaseService.getConfig(key);
  }

  async setConfig(key: string, value: any) {
    await this.databaseService.setConfig(key, value);
    return { success: true, key, value };
  }

  async deleteConfig(key: string) {
    await this.databaseService.deleteConfig(key);
    return { success: true, key };
  }

  // Agent operations
  async getAgents() {
    return await this.databaseService.all('SELECT * FROM agents ORDER BY created_at DESC');
  }

  async getAgent(id: string) {
    return await this.databaseService.get('SELECT * FROM agents WHERE id = ?', [id]);
  }

  async createAgent(agent: any) {
    const id = nanoid();
    const now = new Date().toISOString();

    await this.databaseService.run(
      `INSERT INTO agents (id, name, description, system_prompt, model, temperature, max_tokens, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        agent.name,
        agent.description || '',
        agent.system_prompt || '',
        agent.model || 'gpt-3.5-turbo',
        agent.temperature || 0.7,
        agent.max_tokens || 2048,
        now,
        now,
      ]
    );

    return { id, ...agent };
  }

  async updateAgent(id: string, agent: any) {
    const now = new Date().toISOString();

    await this.databaseService.run(
      `UPDATE agents SET name = ?, description = ?, system_prompt = ?, model = ?,
       temperature = ?, max_tokens = ?, updated_at = ? WHERE id = ?`,
      [
        agent.name,
        agent.description || '',
        agent.system_prompt || '',
        agent.model || 'gpt-3.5-turbo',
        agent.temperature || 0.7,
        agent.max_tokens || 2048,
        now,
        id,
      ]
    );

    return { id, ...agent };
  }

  async deleteAgent(id: string) {
    // Delete related conversations and messages
    const conversations = await this.databaseService.all(
      'SELECT id FROM conversations WHERE agent_id = ?',
      [id]
    );

    for (const conv of conversations) {
      await this.databaseService.run('DELETE FROM messages WHERE conversation_id = ?', [conv.id]);
    }

    await this.databaseService.run('DELETE FROM conversations WHERE agent_id = ?', [id]);
    await this.databaseService.run('DELETE FROM agents WHERE id = ?', [id]);

    return { success: true, id };
  }

  async duplicateAgent(id: string) {
    const agent = await this.getAgent(id);
    if (!agent) {
      throw new Error('Agent not found');
    }

    const newAgent = {
      ...agent,
      name: `${agent.name} (Copy)`,
    };
    delete newAgent.id;
    delete newAgent.created_at;
    delete newAgent.updated_at;

    return await this.createAgent(newAgent);
  }

  // Conversation operations
  async getAgentConversations(agentId: string) {
    return await this.databaseService.all(
      'SELECT * FROM conversations WHERE agent_id = ? ORDER BY updated_at DESC',
      [agentId]
    );
  }

  async createConversation(agentId: string, conversation: any) {
    const id = nanoid();
    const now = new Date().toISOString();

    await this.databaseService.run(
      'INSERT INTO conversations (id, agent_id, title, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
      [id, agentId, conversation.title || 'New Conversation', now, now]
    );

    return { id, agent_id: agentId, ...conversation };
  }

  async deleteConversation(agentId: string, conversationId: string) {
    await this.databaseService.run('DELETE FROM messages WHERE conversation_id = ?', [conversationId]);
    await this.databaseService.run(
      'DELETE FROM conversations WHERE id = ? AND agent_id = ?',
      [conversationId, agentId]
    );

    return { success: true, conversationId };
  }

  // Canvas operations
  async getCanvases() {
    return await this.databaseService.all('SELECT * FROM canvases ORDER BY updated_at DESC');
  }

  async getCanvas(id: string) {
    const canvas = await this.databaseService.get('SELECT * FROM canvases WHERE id = ?', [id]);
    if (canvas && canvas.data) {
      canvas.data = JSON.parse(canvas.data);
    }
    return canvas;
  }

  async createCanvas(canvas: any) {
    const id = nanoid();
    const now = new Date().toISOString();

    await this.databaseService.run(
      'INSERT INTO canvases (id, name, description, data, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
      [id, canvas.name, canvas.description || '', JSON.stringify(canvas.data || {}), now, now]
    );

    return { id, ...canvas };
  }

  async updateCanvas(id: string, canvas: any) {
    const now = new Date().toISOString();

    await this.databaseService.run(
      'UPDATE canvases SET name = ?, description = ?, data = ?, updated_at = ? WHERE id = ?',
      [canvas.name, canvas.description || '', JSON.stringify(canvas.data || {}), now, id]
    );

    return { id, ...canvas };
  }

  async deleteCanvas(id: string) {
    await this.databaseService.run('DELETE FROM canvas_nodes WHERE canvas_id = ?', [id]);
    await this.databaseService.run('DELETE FROM canvases WHERE id = ?', [id]);

    return { success: true, id };
  }

  async duplicateCanvas(id: string) {
    const canvas = await this.getCanvas(id);
    if (!canvas) {
      throw new Error('Canvas not found');
    }

    const newCanvas = {
      ...canvas,
      name: `${canvas.name} (Copy)`,
    };
    delete newCanvas.id;
    delete newCanvas.created_at;
    delete newCanvas.updated_at;

    return await this.createCanvas(newCanvas);
  }

  // Canvas nodes operations
  async getCanvasNodes(canvasId: string) {
    const nodes = await this.databaseService.all(
      'SELECT * FROM canvas_nodes WHERE canvas_id = ? ORDER BY created_at',
      [canvasId]
    );
    return nodes.map(node => ({
      ...node,
      data: node.data ? JSON.parse(node.data) : {}
    }));
  }

  async createCanvasNode(canvasId: string, node: any) {
    const id = nanoid();
    const now = new Date().toISOString();

    await this.databaseService.run(
      `INSERT INTO canvas_nodes (id, canvas_id, type, position_x, position_y, data, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        canvasId,
        node.type,
        node.position?.x || 0,
        node.position?.y || 0,
        JSON.stringify(node.data || {}),
        now,
        now,
      ]
    );

    return { id, canvas_id: canvasId, ...node };
  }

  async updateCanvasNode(canvasId: string, nodeId: string, node: any) {
    const now = new Date().toISOString();

    await this.databaseService.run(
      `UPDATE canvas_nodes SET type = ?, position_x = ?, position_y = ?, data = ?, updated_at = ?
       WHERE id = ? AND canvas_id = ?`,
      [
        node.type,
        node.position?.x || 0,
        node.position?.y || 0,
        JSON.stringify(node.data || {}),
        now,
        nodeId,
        canvasId,
      ]
    );

    return { id: nodeId, canvas_id: canvasId, ...node };
  }

  async deleteCanvasNode(canvasId: string, nodeId: string) {
    await this.databaseService.run(
      'DELETE FROM canvas_nodes WHERE id = ? AND canvas_id = ?',
      [nodeId, canvasId]
    );

    return { success: true, nodeId };
  }

  // LLM Models operations
  async getLLMModels() {
    return await this.databaseService.all('SELECT * FROM llm_models ORDER BY created_at DESC');
  }

  async addLLMModel(model: any) {
    const id = nanoid();
    const now = new Date().toISOString();

    await this.databaseService.run(
      `INSERT INTO llm_models (id, name, provider, api_key, base_url, model_id, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        model.name,
        model.provider,
        model.api_key || '',
        model.base_url || '',
        model.model_id || '',
        now,
        now,
      ]
    );

    return { id, ...model };
  }

  async deleteLLMModel(id: string) {
    await this.databaseService.run('DELETE FROM llm_models WHERE id = ?', [id]);
    return { success: true, id };
  }

  // Settings operations
  async getSettings() {
    const settings = await this.getConfig('settings');
    return settings || {
      theme: 'dark',
      language: 'en',
      workspace: {},
      ai: {},
    };
  }

  async updateSettings(settings: any) {
    await this.setConfig('settings', settings);
    return settings;
  }

  async getTheme() {
    const settings = await this.getSettings();
    return { theme: settings.theme || 'dark' };
  }

  async updateTheme(theme: string) {
    const settings = await this.getSettings();
    settings.theme = theme;
    await this.updateSettings(settings);
    return { theme };
  }

  async getLanguage() {
    const settings = await this.getSettings();
    return { language: settings.language || 'en' };
  }

  async updateLanguage(language: string) {
    const settings = await this.getSettings();
    settings.language = language;
    await this.updateSettings(settings);
    return { language };
  }

  async getWorkspaceSettings() {
    const settings = await this.getSettings();
    return settings.workspace || {};
  }

  async updateWorkspaceSettings(workspaceSettings: any) {
    const settings = await this.getSettings();
    settings.workspace = workspaceSettings;
    await this.updateSettings(settings);
    return workspaceSettings;
  }

  async getAISettings() {
    const settings = await this.getSettings();
    return settings.ai || {};
  }

  async updateAISettings(aiSettings: any) {
    const settings = await this.getSettings();
    settings.ai = aiSettings;
    await this.updateSettings(settings);
    return aiSettings;
  }

  async resetSettings() {
    const defaultSettings = {
      theme: 'dark',
      language: 'en',
      workspace: {},
      ai: {},
    };
    await this.updateSettings(defaultSettings);
    return defaultSettings;
  }

  async exportSettings() {
    const settings = await this.getSettings();
    const agents = await this.getAgents();
    const canvases = await this.getCanvases();
    const llmModels = await this.getLLMModels();

    return {
      settings,
      agents,
      canvases,
      llmModels,
      exportedAt: new Date().toISOString(),
    };
  }

  async importSettings(data: any) {
    if (data.settings) {
      await this.updateSettings(data.settings);
    }

    // Import agents
    if (data.agents && Array.isArray(data.agents)) {
      for (const agent of data.agents) {
        delete agent.id;
        delete agent.created_at;
        delete agent.updated_at;
        await this.createAgent(agent);
      }
    }

    // Import canvases
    if (data.canvases && Array.isArray(data.canvases)) {
      for (const canvas of data.canvases) {
        delete canvas.id;
        delete canvas.created_at;
        delete canvas.updated_at;
        await this.createCanvas(canvas);
      }
    }

    // Import LLM models
    if (data.llmModels && Array.isArray(data.llmModels)) {
      for (const model of data.llmModels) {
        delete model.id;
        delete model.created_at;
        delete model.updated_at;
        await this.addLLMModel(model);
      }
    }

    return { success: true, message: 'Settings imported successfully' };
  }

  // Model list operations
  async getModelList() {
    const config = await this.getConfig('providers') || this.getDefaultProvidersConfig();
    const result: any[] = [];

    // Add default models from configuration
    for (const [provider, providerConfig] of Object.entries(config)) {
      const typedConfig = providerConfig as any;
      const models = typedConfig.models || {};
      for (const [modelName, modelConfig] of Object.entries(models)) {
        const typedModelConfig = modelConfig as any;
        result.push({
          provider,
          model: modelName,
          url: typedConfig.url || '',
          type: typedModelConfig.type || 'text'
        });
      }
    }

    return result;
  }

  private getDefaultProvidersConfig() {
    return {
      openai: {
        models: {
          'gpt-3.5-turbo': { type: 'text' },
          'gpt-4': { type: 'text' },
          'gpt-4-turbo': { type: 'text' },
          'dall-e-3': { type: 'image' }
        },
        url: 'https://api.openai.com/v1/',
        api_key: '',
        max_tokens: 4096
      },
      anthropic: {
        models: {
          'claude-3-sonnet-20240229': { type: 'text' },
          'claude-3-haiku-20240307': { type: 'text' },
          'claude-3-opus-20240229': { type: 'text' }
        },
        url: 'https://api.anthropic.com/',
        api_key: '',
        max_tokens: 4096
      },
      ollama: {
        models: {},
        url: 'http://localhost:11434',
        api_key: '',
        max_tokens: 8192
      },
      comfyui: {
        models: {
          'flux-dev': { type: 'image' },
          'flux-schnell': { type: 'image' },
          'sdxl': { type: 'image' }
        },
        url: 'http://127.0.0.1:8188',
        api_key: ''
      }
    };
  }

  // Chat session operations
  async getChatSession(sessionId: string) {
    // First try to find by conversation_id
    const messages = await this.databaseService.all(
      'SELECT * FROM messages WHERE conversation_id = ? ORDER BY created_at ASC',
      [sessionId]
    );

    return messages.map((msg: any) => ({
      role: msg.role,
      content: msg.content,
      id: msg.id,
      created_at: msg.created_at
    }));
  }

  async listChatSessions() {
    // Get conversations instead of sessions
    const conversations = await this.databaseService.all(
      'SELECT * FROM conversations ORDER BY updated_at DESC'
    );

    return conversations.map((conv: any) => ({
      id: conv.id,
      title: conv.title || `Conversation ${conv.id.substring(0, 8)}`,
      created_at: conv.created_at,
      updated_at: conv.updated_at
    }));
  }
}
