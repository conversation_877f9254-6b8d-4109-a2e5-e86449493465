"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CanvasController = void 0;
const common_1 = require("@nestjs/common");
const config_service_1 = require("../services/config.service");
let CanvasController = class CanvasController {
    configService;
    constructor(configService) {
        this.configService = configService;
    }
    async getAllCanvases() {
        return await this.configService.getCanvases();
    }
    async getCanvas(id) {
        return await this.configService.getCanvas(id);
    }
    async createCanvas(canvas) {
        return await this.configService.createCanvas(canvas);
    }
    async updateCanvas(id, canvas) {
        return await this.configService.updateCanvas(id, canvas);
    }
    async deleteCanvas(id) {
        return await this.configService.deleteCanvas(id);
    }
    async duplicateCanvas(id) {
        return await this.configService.duplicateCanvas(id);
    }
    async getCanvasNodes(id) {
        return await this.configService.getCanvasNodes(id);
    }
    async createCanvasNode(id, node) {
        return await this.configService.createCanvasNode(id, node);
    }
    async updateCanvasNode(id, nodeId, node) {
        return await this.configService.updateCanvasNode(id, nodeId, node);
    }
    async deleteCanvasNode(id, nodeId) {
        return await this.configService.deleteCanvasNode(id, nodeId);
    }
};
exports.CanvasController = CanvasController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "getAllCanvases", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "getCanvas", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "createCanvas", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "updateCanvas", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "deleteCanvas", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "duplicateCanvas", null);
__decorate([
    (0, common_1.Get)(':id/nodes'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "getCanvasNodes", null);
__decorate([
    (0, common_1.Post)(':id/nodes'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "createCanvasNode", null);
__decorate([
    (0, common_1.Put)(':id/nodes/:nodeId'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('nodeId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "updateCanvasNode", null);
__decorate([
    (0, common_1.Delete)(':id/nodes/:nodeId'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('nodeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CanvasController.prototype, "deleteCanvasNode", null);
exports.CanvasController = CanvasController = __decorate([
    (0, common_1.Controller)('api/canvas'),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], CanvasController);
//# sourceMappingURL=canvas.controller.js.map