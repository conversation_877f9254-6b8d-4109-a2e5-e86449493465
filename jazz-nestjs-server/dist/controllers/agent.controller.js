"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelController = exports.AgentController = void 0;
const common_1 = require("@nestjs/common");
const config_service_1 = require("../services/config.service");
let AgentController = class AgentController {
    configService;
    constructor(configService) {
        this.configService = configService;
    }
    async getAllAgents() {
        return await this.configService.getAgents();
    }
    async getAgent(id) {
        return await this.configService.getAgent(id);
    }
    async createAgent(agent) {
        return await this.configService.createAgent(agent);
    }
    async updateAgent(id, agent) {
        return await this.configService.updateAgent(id, agent);
    }
    async deleteAgent(id) {
        return await this.configService.deleteAgent(id);
    }
    async duplicateAgent(id) {
        return await this.configService.duplicateAgent(id);
    }
    async getAgentConversations(id) {
        return await this.configService.getAgentConversations(id);
    }
    async createConversation(id, conversation) {
        return await this.configService.createConversation(id, conversation);
    }
    async deleteConversation(id, conversationId) {
        return await this.configService.deleteConversation(id, conversationId);
    }
};
exports.AgentController = AgentController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "getAllAgents", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "getAgent", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "createAgent", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "updateAgent", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "deleteAgent", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "duplicateAgent", null);
__decorate([
    (0, common_1.Get)(':id/conversations'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "getAgentConversations", null);
__decorate([
    (0, common_1.Post)(':id/conversations'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "createConversation", null);
__decorate([
    (0, common_1.Delete)(':id/conversations/:conversationId'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('conversationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgentController.prototype, "deleteConversation", null);
exports.AgentController = AgentController = __decorate([
    (0, common_1.Controller)('api/agent'),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], AgentController);
let ModelController = class ModelController {
    configService;
    constructor(configService) {
        this.configService = configService;
    }
    async getModels() {
        return await this.configService.getModelList();
    }
    async getChatSession(sessionId) {
        return await this.configService.getChatSession(sessionId);
    }
    async listChatSessions() {
        return await this.configService.listChatSessions();
    }
    async handleChat(data) {
        return { status: 'done' };
    }
    async getProviders() {
        return await this.configService.getProviders();
    }
};
exports.ModelController = ModelController;
__decorate([
    (0, common_1.Get)('list_models'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "getModels", null);
__decorate([
    (0, common_1.Get)('chat_session/:sessionId'),
    __param(0, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "getChatSession", null);
__decorate([
    (0, common_1.Get)('list_chat_sessions'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "listChatSessions", null);
__decorate([
    (0, common_1.Post)('chat'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "handleChat", null);
__decorate([
    (0, common_1.Get)('providers'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ModelController.prototype, "getProviders", null);
exports.ModelController = ModelController = __decorate([
    (0, common_1.Controller)('api'),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], ModelController);
//# sourceMappingURL=agent.controller.js.map