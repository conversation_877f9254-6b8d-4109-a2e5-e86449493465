import { ConfigService } from '../services/config.service';
export declare class AgentController {
    private readonly configService;
    constructor(configService: ConfigService);
    getAllAgents(): Promise<any[]>;
    getAgent(id: string): Promise<any>;
    createAgent(agent: any): Promise<any>;
    updateAgent(id: string, agent: any): Promise<any>;
    deleteAgent(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
    duplicateAgent(id: string): Promise<any>;
    getAgentConversations(id: string): Promise<any[]>;
    createConversation(id: string, conversation: any): Promise<any>;
    deleteConversation(id: string, conversationId: string): Promise<{
        success: boolean;
        conversationId: string;
    }>;
}
export declare class ModelController {
    private readonly configService;
    constructor(configService: ConfigService);
    getModels(): Promise<any[]>;
}
