"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("./database.service");
const nanoid_1 = require("nanoid");
let ConfigService = class ConfigService {
    databaseService;
    constructor(databaseService) {
        this.databaseService = databaseService;
    }
    async getAllConfigs() {
        const configs = await this.databaseService.getAllConfigs();
        const validConfigs = configs.filter((config) => config && config.key && config.key.trim() !== '');
        if (validConfigs.length === 0) {
            return this.getDefaultProvidersConfig();
        }
        const configObject = {};
        for (const config of validConfigs) {
            try {
                configObject[config.key] = typeof config.value === 'string'
                    ? JSON.parse(config.value)
                    : config.value;
            }
            catch (e) {
                console.warn(`Invalid JSON in config ${config.key}:`, config.value);
            }
        }
        if (!configObject.providers && Object.keys(configObject).length > 0) {
            configObject.providers = this.getDefaultProvidersConfig();
        }
        return Object.keys(configObject).length > 0 ? configObject : this.getDefaultProvidersConfig();
    }
    async getConfig(key) {
        return await this.databaseService.getConfig(key);
    }
    async setConfig(key, value) {
        await this.databaseService.setConfig(key, value);
        return { success: true, key, value };
    }
    async deleteConfig(key) {
        await this.databaseService.deleteConfig(key);
        return { success: true, key };
    }
    async getAgents() {
        return await this.databaseService.all('SELECT * FROM agents ORDER BY created_at DESC');
    }
    async getAgent(id) {
        return await this.databaseService.get('SELECT * FROM agents WHERE id = ?', [id]);
    }
    async createAgent(agent) {
        const id = (0, nanoid_1.nanoid)();
        const now = new Date().toISOString();
        await this.databaseService.run(`INSERT INTO agents (id, name, description, system_prompt, model, temperature, max_tokens, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
            id,
            agent.name,
            agent.description || '',
            agent.system_prompt || '',
            agent.model || 'gpt-3.5-turbo',
            agent.temperature || 0.7,
            agent.max_tokens || 2048,
            now,
            now,
        ]);
        return { id, ...agent };
    }
    async updateAgent(id, agent) {
        const now = new Date().toISOString();
        await this.databaseService.run(`UPDATE agents SET name = ?, description = ?, system_prompt = ?, model = ?,
       temperature = ?, max_tokens = ?, updated_at = ? WHERE id = ?`, [
            agent.name,
            agent.description || '',
            agent.system_prompt || '',
            agent.model || 'gpt-3.5-turbo',
            agent.temperature || 0.7,
            agent.max_tokens || 2048,
            now,
            id,
        ]);
        return { id, ...agent };
    }
    async deleteAgent(id) {
        const conversations = await this.databaseService.all('SELECT id FROM conversations WHERE agent_id = ?', [id]);
        for (const conv of conversations) {
            await this.databaseService.run('DELETE FROM messages WHERE conversation_id = ?', [conv.id]);
        }
        await this.databaseService.run('DELETE FROM conversations WHERE agent_id = ?', [id]);
        await this.databaseService.run('DELETE FROM agents WHERE id = ?', [id]);
        return { success: true, id };
    }
    async duplicateAgent(id) {
        const agent = await this.getAgent(id);
        if (!agent) {
            throw new Error('Agent not found');
        }
        const newAgent = {
            ...agent,
            name: `${agent.name} (Copy)`,
        };
        delete newAgent.id;
        delete newAgent.created_at;
        delete newAgent.updated_at;
        return await this.createAgent(newAgent);
    }
    async getAgentConversations(agentId) {
        return await this.databaseService.all('SELECT * FROM conversations WHERE agent_id = ? ORDER BY updated_at DESC', [agentId]);
    }
    async createConversation(agentId, conversation) {
        const id = (0, nanoid_1.nanoid)();
        const now = new Date().toISOString();
        await this.databaseService.run('INSERT INTO conversations (id, agent_id, title, created_at, updated_at) VALUES (?, ?, ?, ?, ?)', [id, agentId, conversation.title || 'New Conversation', now, now]);
        return { id, agent_id: agentId, ...conversation };
    }
    async deleteConversation(agentId, conversationId) {
        await this.databaseService.run('DELETE FROM messages WHERE conversation_id = ?', [conversationId]);
        await this.databaseService.run('DELETE FROM conversations WHERE id = ? AND agent_id = ?', [conversationId, agentId]);
        return { success: true, conversationId };
    }
    async getCanvases() {
        return await this.databaseService.all('SELECT * FROM canvases ORDER BY updated_at DESC');
    }
    async getCanvas(id) {
        const canvas = await this.databaseService.get('SELECT * FROM canvases WHERE id = ?', [id]);
        if (canvas && canvas.data) {
            canvas.data = JSON.parse(canvas.data);
        }
        return canvas;
    }
    async createCanvas(canvas) {
        const id = (0, nanoid_1.nanoid)();
        const now = new Date().toISOString();
        await this.databaseService.run('INSERT INTO canvases (id, name, description, data, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)', [id, canvas.name, canvas.description || '', JSON.stringify(canvas.data || {}), now, now]);
        return { id, ...canvas };
    }
    async updateCanvas(id, canvas) {
        const now = new Date().toISOString();
        await this.databaseService.run('UPDATE canvases SET name = ?, description = ?, data = ?, updated_at = ? WHERE id = ?', [canvas.name, canvas.description || '', JSON.stringify(canvas.data || {}), now, id]);
        return { id, ...canvas };
    }
    async deleteCanvas(id) {
        await this.databaseService.run('DELETE FROM canvas_nodes WHERE canvas_id = ?', [id]);
        await this.databaseService.run('DELETE FROM canvases WHERE id = ?', [id]);
        return { success: true, id };
    }
    async duplicateCanvas(id) {
        const canvas = await this.getCanvas(id);
        if (!canvas) {
            throw new Error('Canvas not found');
        }
        const newCanvas = {
            ...canvas,
            name: `${canvas.name} (Copy)`,
        };
        delete newCanvas.id;
        delete newCanvas.created_at;
        delete newCanvas.updated_at;
        return await this.createCanvas(newCanvas);
    }
    async getCanvasNodes(canvasId) {
        const nodes = await this.databaseService.all('SELECT * FROM canvas_nodes WHERE canvas_id = ? ORDER BY created_at', [canvasId]);
        return nodes.map(node => ({
            ...node,
            data: node.data ? JSON.parse(node.data) : {}
        }));
    }
    async createCanvasNode(canvasId, node) {
        const id = (0, nanoid_1.nanoid)();
        const now = new Date().toISOString();
        await this.databaseService.run(`INSERT INTO canvas_nodes (id, canvas_id, type, position_x, position_y, data, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [
            id,
            canvasId,
            node.type,
            node.position?.x || 0,
            node.position?.y || 0,
            JSON.stringify(node.data || {}),
            now,
            now,
        ]);
        return { id, canvas_id: canvasId, ...node };
    }
    async updateCanvasNode(canvasId, nodeId, node) {
        const now = new Date().toISOString();
        await this.databaseService.run(`UPDATE canvas_nodes SET type = ?, position_x = ?, position_y = ?, data = ?, updated_at = ?
       WHERE id = ? AND canvas_id = ?`, [
            node.type,
            node.position?.x || 0,
            node.position?.y || 0,
            JSON.stringify(node.data || {}),
            now,
            nodeId,
            canvasId,
        ]);
        return { id: nodeId, canvas_id: canvasId, ...node };
    }
    async deleteCanvasNode(canvasId, nodeId) {
        await this.databaseService.run('DELETE FROM canvas_nodes WHERE id = ? AND canvas_id = ?', [nodeId, canvasId]);
        return { success: true, nodeId };
    }
    async getLLMModels() {
        return await this.databaseService.all('SELECT * FROM llm_models ORDER BY created_at DESC');
    }
    async addLLMModel(model) {
        const id = (0, nanoid_1.nanoid)();
        const now = new Date().toISOString();
        await this.databaseService.run(`INSERT INTO llm_models (id, name, provider, api_key, base_url, model_id, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [
            id,
            model.name,
            model.provider,
            model.api_key || '',
            model.base_url || '',
            model.model_id || '',
            now,
            now,
        ]);
        return { id, ...model };
    }
    async deleteLLMModel(id) {
        await this.databaseService.run('DELETE FROM llm_models WHERE id = ?', [id]);
        return { success: true, id };
    }
    async getSettings() {
        const settings = await this.getConfig('settings');
        return settings || {
            theme: 'dark',
            language: 'en',
            workspace: {},
            ai: {},
        };
    }
    async updateSettings(settings) {
        await this.setConfig('settings', settings);
        return settings;
    }
    async getTheme() {
        const settings = await this.getSettings();
        return { theme: settings.theme || 'dark' };
    }
    async updateTheme(theme) {
        const settings = await this.getSettings();
        settings.theme = theme;
        await this.updateSettings(settings);
        return { theme };
    }
    async getLanguage() {
        const settings = await this.getSettings();
        return { language: settings.language || 'en' };
    }
    async updateLanguage(language) {
        const settings = await this.getSettings();
        settings.language = language;
        await this.updateSettings(settings);
        return { language };
    }
    async getWorkspaceSettings() {
        const settings = await this.getSettings();
        return settings.workspace || {};
    }
    async updateWorkspaceSettings(workspaceSettings) {
        const settings = await this.getSettings();
        settings.workspace = workspaceSettings;
        await this.updateSettings(settings);
        return workspaceSettings;
    }
    async getAISettings() {
        const settings = await this.getSettings();
        return settings.ai || {};
    }
    async updateAISettings(aiSettings) {
        const settings = await this.getSettings();
        settings.ai = aiSettings;
        await this.updateSettings(settings);
        return aiSettings;
    }
    async resetSettings() {
        const defaultSettings = {
            theme: 'dark',
            language: 'en',
            workspace: {},
            ai: {},
        };
        await this.updateSettings(defaultSettings);
        return defaultSettings;
    }
    async exportSettings() {
        const settings = await this.getSettings();
        const agents = await this.getAgents();
        const canvases = await this.getCanvases();
        const llmModels = await this.getLLMModels();
        return {
            settings,
            agents,
            canvases,
            llmModels,
            exportedAt: new Date().toISOString(),
        };
    }
    async importSettings(data) {
        if (data.settings) {
            await this.updateSettings(data.settings);
        }
        if (data.agents && Array.isArray(data.agents)) {
            for (const agent of data.agents) {
                delete agent.id;
                delete agent.created_at;
                delete agent.updated_at;
                await this.createAgent(agent);
            }
        }
        if (data.canvases && Array.isArray(data.canvases)) {
            for (const canvas of data.canvases) {
                delete canvas.id;
                delete canvas.created_at;
                delete canvas.updated_at;
                await this.createCanvas(canvas);
            }
        }
        if (data.llmModels && Array.isArray(data.llmModels)) {
            for (const model of data.llmModels) {
                delete model.id;
                delete model.created_at;
                delete model.updated_at;
                await this.addLLMModel(model);
            }
        }
        return { success: true, message: 'Settings imported successfully' };
    }
    async getModelList() {
        const config = await this.getConfig('providers') || this.getDefaultProvidersConfig();
        const result = [];
        for (const [provider, providerConfig] of Object.entries(config)) {
            const typedConfig = providerConfig;
            const models = typedConfig.models || {};
            for (const [modelName, modelConfig] of Object.entries(models)) {
                const typedModelConfig = modelConfig;
                result.push({
                    provider,
                    model: modelName,
                    url: typedConfig.url || '',
                    type: typedModelConfig.type || 'text'
                });
            }
        }
        return result;
    }
    async getProviders() {
        let config = await this.getConfig('providers');
        if (!config) {
            config = this.getDefaultProvidersConfig();
        }
        if (Array.isArray(config)) {
            const arrayConfig = config;
            const cleanConfig = {};
            for (const item of arrayConfig) {
                if (item && typeof item === 'object' && item.key && item.value) {
                    try {
                        const parsedValue = typeof item.value === 'string' ? JSON.parse(item.value) : item.value;
                        cleanConfig[item.key] = parsedValue;
                    }
                    catch (e) {
                    }
                }
            }
            if (Object.keys(cleanConfig).length > 0) {
                return cleanConfig;
            }
            else {
                return this.getDefaultProvidersConfig();
            }
        }
        const cleanConfig = {};
        for (const [key, value] of Object.entries(config)) {
            if (key && value && typeof value === 'object') {
                cleanConfig[key] = value;
            }
        }
        if (Object.keys(cleanConfig).length === 0) {
            return this.getDefaultProvidersConfig();
        }
        return cleanConfig;
    }
    getDefaultProvidersConfig() {
        return {
            openai: {
                models: {
                    'gpt-4o': { type: 'text' },
                    'gpt-4o-mini': { type: 'text' },
                    'gpt-image-1': { type: 'image' }
                },
                url: 'https://api.openai.com/v1/',
                api_key: '',
                max_tokens: 4096
            },
            anthropic: {
                models: {
                    'claude-3-sonnet-20240229': { type: 'text' },
                    'claude-3-haiku-20240307': { type: 'text' },
                    'claude-3-opus-20240229': { type: 'text' }
                },
                url: 'https://api.anthropic.com/',
                api_key: '',
                max_tokens: 4096
            },
            ollama: {
                models: {},
                url: 'http://localhost:11434',
                api_key: '',
                max_tokens: 8192
            },
            comfyui: {
                models: {
                    'flux-dev': { type: 'image' },
                    'flux-schnell': { type: 'image' },
                    'sdxl': { type: 'image' }
                },
                url: 'http://127.0.0.1:8188',
                api_key: ''
            },
            jaaz: {
                models: {
                    'gpt-4o': { type: 'text' },
                    'gpt-4o-mini': { type: 'text' }
                },
                url: 'http://localhost:3000/api/v1/',
                api_key: '',
                max_tokens: 4096
            },
            replicate: {
                models: {
                    'google/imagen-4': { type: 'image' },
                    'black-forest-labs/flux-1.1-pro': { type: 'image' }
                },
                url: 'https://api.replicate.com/v1/',
                api_key: '',
                max_tokens: 4096
            },
            wavespeed: {
                models: {
                    'wavespeed-ai/flux-dev': { type: 'image' }
                },
                url: 'https://api.wavespeed.ai/api/v3/',
                api_key: '',
                max_tokens: 4096
            },
            '深度求索': {
                models: {
                    'deepseek-chat': { type: 'text' }
                },
                url: 'https://api.deepseek.com/v1/',
                api_key: '',
                max_tokens: 4096
            }
        };
    }
    async getChatSession(sessionId) {
        const messages = await this.databaseService.all('SELECT * FROM messages WHERE conversation_id = ? ORDER BY created_at ASC', [sessionId]);
        return messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
            id: msg.id,
            created_at: msg.created_at
        }));
    }
    async listChatSessions() {
        const conversations = await this.databaseService.all('SELECT * FROM conversations ORDER BY updated_at DESC');
        return conversations.map((conv) => ({
            id: conv.id,
            title: conv.title || `Conversation ${conv.id.substring(0, 8)}`,
            created_at: conv.created_at,
            updated_at: conv.updated_at
        }));
    }
    async getCanvasSessions(canvasId) {
        const canvas = await this.getCanvas(canvasId);
        if (!canvas || !canvas.data) {
            return [];
        }
        const canvasData = canvas.data;
        if (canvasData.session_id) {
            return [{
                    id: canvasData.session_id,
                    title: `Session ${canvasData.session_id.substring(0, 8)}`,
                    created_at: canvas.created_at,
                    updated_at: canvas.updated_at,
                    model: canvasData.text_model?.model || 'gpt-3.5-turbo',
                    provider: canvasData.text_model?.provider || 'openai'
                }];
        }
        return [];
    }
};
exports.ConfigService = ConfigService;
exports.ConfigService = ConfigService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], ConfigService);
//# sourceMappingURL=config.service.js.map