import { DatabaseService } from './database.service';
export declare class ConfigService {
    private readonly databaseService;
    constructor(databaseService: DatabaseService);
    getAllConfigs(): Promise<any>;
    getConfig(key: string): Promise<any>;
    setConfig(key: string, value: any): Promise<{
        success: boolean;
        key: string;
        value: any;
    }>;
    deleteConfig(key: string): Promise<{
        success: boolean;
        key: string;
    }>;
    getAgents(): Promise<any[]>;
    getAgent(id: string): Promise<any>;
    createAgent(agent: any): Promise<any>;
    updateAgent(id: string, agent: any): Promise<any>;
    deleteAgent(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
    duplicateAgent(id: string): Promise<any>;
    getAgentConversations(agentId: string): Promise<any[]>;
    createConversation(agentId: string, conversation: any): Promise<any>;
    deleteConversation(agentId: string, conversationId: string): Promise<{
        success: boolean;
        conversationId: string;
    }>;
    getCanvases(): Promise<any[]>;
    getCanvas(id: string): Promise<any>;
    createCanvas(canvas: any): Promise<any>;
    updateCanvas(id: string, canvas: any): Promise<any>;
    deleteCanvas(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
    duplicateCanvas(id: string): Promise<any>;
    getCanvasNodes(canvasId: string): Promise<any[]>;
    createCanvasNode(canvasId: string, node: any): Promise<any>;
    updateCanvasNode(canvasId: string, nodeId: string, node: any): Promise<any>;
    deleteCanvasNode(canvasId: string, nodeId: string): Promise<{
        success: boolean;
        nodeId: string;
    }>;
    getLLMModels(): Promise<any[]>;
    addLLMModel(model: any): Promise<any>;
    deleteLLMModel(id: string): Promise<{
        success: boolean;
        id: string;
    }>;
    getSettings(): Promise<any>;
    updateSettings(settings: any): Promise<any>;
    getTheme(): Promise<{
        theme: any;
    }>;
    updateTheme(theme: string): Promise<{
        theme: string;
    }>;
    getLanguage(): Promise<{
        language: any;
    }>;
    updateLanguage(language: string): Promise<{
        language: string;
    }>;
    getWorkspaceSettings(): Promise<any>;
    updateWorkspaceSettings(workspaceSettings: any): Promise<any>;
    getAISettings(): Promise<any>;
    updateAISettings(aiSettings: any): Promise<any>;
    resetSettings(): Promise<{
        theme: string;
        language: string;
        workspace: {};
        ai: {};
    }>;
    exportSettings(): Promise<{
        settings: any;
        agents: any[];
        canvases: any[];
        llmModels: any[];
        exportedAt: string;
    }>;
    importSettings(data: any): Promise<{
        success: boolean;
        message: string;
    }>;
    getModelList(): Promise<any[]>;
    getProviders(): Promise<any>;
    private getDefaultProvidersConfig;
    getChatSession(sessionId: string): Promise<{
        role: any;
        content: any;
        id: any;
        created_at: any;
    }[]>;
    listChatSessions(): Promise<{
        id: any;
        title: any;
        created_at: any;
        updated_at: any;
    }[]>;
    getCanvasSessions(canvasId: string): Promise<{
        id: any;
        title: string;
        created_at: any;
        updated_at: any;
        model: any;
        provider: any;
    }[]>;
}
