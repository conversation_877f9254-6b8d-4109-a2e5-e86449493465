"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const chat_service_1 = require("../services/chat.service");
const config_service_1 = require("../services/config.service");
let WebSocketGateway = class WebSocketGateway {
    chatService;
    configService;
    server;
    connectedClients = new Map();
    constructor(chatService, configService) {
        this.chatService = chatService;
        this.configService = configService;
    }
    handleConnection(client) {
        console.log(`Client connected: ${client.id}`);
        this.connectedClients.set(client.id, client);
    }
    handleDisconnect(client) {
        console.log(`Client disconnected: ${client.id}`);
        this.connectedClients.delete(client.id);
    }
    handleJoinConversation(client, data) {
        client.join(`conversation_${data.conversationId}`);
        return { event: 'joined_conversation', data: { conversationId: data.conversationId } };
    }
    handleLeaveConversation(client, data) {
        client.leave(`conversation_${data.conversationId}`);
        return { event: 'left_conversation', data: { conversationId: data.conversationId } };
    }
    async handleSendMessage(client, data) {
        try {
            const response = await this.chatService.sendMessage(data.conversationId, data.message);
            this.server.to(`conversation_${data.conversationId}`).emit('message_received', {
                conversationId: data.conversationId,
                message: response,
            });
            return { event: 'message_sent', data: response };
        }
        catch (error) {
            client.emit('error', { message: error.message });
        }
    }
    async handleStreamMessage(client, data) {
        try {
            const stream = this.chatService.streamChat(data.conversationId, data.message);
            stream.subscribe({
                next: (chunk) => {
                    client.emit('stream_chunk', {
                        conversationId: data.conversationId,
                        chunk: chunk.data,
                    });
                },
                complete: () => {
                    client.emit('stream_complete', {
                        conversationId: data.conversationId,
                    });
                },
                error: (error) => {
                    client.emit('stream_error', {
                        conversationId: data.conversationId,
                        error: error.message,
                    });
                },
            });
            return { event: 'stream_started', data: { conversationId: data.conversationId } };
        }
        catch (error) {
            client.emit('error', { message: error.message });
        }
    }
    async handleStopGeneration(client, data) {
        try {
            await this.chatService.stopGeneration(data.conversationId);
            this.server.to(`conversation_${data.conversationId}`).emit('generation_stopped', {
                conversationId: data.conversationId,
            });
            return { event: 'generation_stopped', data: { conversationId: data.conversationId } };
        }
        catch (error) {
            client.emit('error', { message: error.message });
        }
    }
    async handleCanvasUpdate(client, data) {
        try {
            await this.configService.updateCanvas(data.canvasId, data.update);
            client.broadcast.emit('canvas_updated', {
                canvasId: data.canvasId,
                update: data.update,
            });
            return { event: 'canvas_update_sent', data: { canvasId: data.canvasId } };
        }
        catch (error) {
            client.emit('error', { message: error.message });
        }
    }
    broadcastToAll(event, data) {
        this.server.emit(event, data);
    }
    broadcastToConversation(conversationId, event, data) {
        this.server.to(`conversation_${conversationId}`).emit(event, data);
    }
};
exports.WebSocketGateway = WebSocketGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], WebSocketGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('join_conversation'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", void 0)
], WebSocketGateway.prototype, "handleJoinConversation", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('leave_conversation'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", void 0)
], WebSocketGateway.prototype, "handleLeaveConversation", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('send_message'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], WebSocketGateway.prototype, "handleSendMessage", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('stream_message'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], WebSocketGateway.prototype, "handleStreamMessage", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('stop_generation'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], WebSocketGateway.prototype, "handleStopGeneration", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('canvas_update'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], WebSocketGateway.prototype, "handleCanvasUpdate", null);
exports.WebSocketGateway = WebSocketGateway = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: ['http://localhost:3000', 'http://localhost:5173'],
            credentials: true,
        },
    }),
    __metadata("design:paramtypes", [chat_service_1.ChatService,
        config_service_1.ConfigService])
], WebSocketGateway);
//# sourceMappingURL=websocket.gateway.js.map