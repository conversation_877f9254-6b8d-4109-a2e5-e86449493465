import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { ChatService } from '../services/chat.service';
import { ConfigService } from '../services/config.service';
export declare class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private readonly chatService;
    private readonly configService;
    server: Server;
    private connectedClients;
    constructor(chatService: ChatService, configService: ConfigService);
    handleConnection(client: Socket): void;
    handleDisconnect(client: Socket): void;
    handleJoinConversation(client: Socket, data: {
        conversationId: string;
    }): {
        event: string;
        data: {
            conversationId: string;
        };
    };
    handleLeaveConversation(client: Socket, data: {
        conversationId: string;
    }): {
        event: string;
        data: {
            conversationId: string;
        };
    };
    handleSendMessage(client: Socket, data: {
        conversationId: string;
        message: any;
    }): Promise<{
        event: string;
        data: {
            id: string;
            conversation_id: string;
            role: string;
            content: string;
            metadata: {
                model: any;
                temperature: any;
                max_tokens: any;
                usage: {
                    prompt_tokens: number;
                    completion_tokens: number;
                    total_tokens: number;
                };
            };
            created_at: string;
        };
    } | undefined>;
    handleStreamMessage(client: Socket, data: {
        conversationId: string;
        message: any;
    }): Promise<{
        event: string;
        data: {
            conversationId: string;
        };
    } | undefined>;
    handleStopGeneration(client: Socket, data: {
        conversationId: string;
    }): Promise<{
        event: string;
        data: {
            conversationId: string;
        };
    } | undefined>;
    handleCanvasUpdate(client: Socket, data: {
        canvasId: string;
        update: any;
    }): Promise<{
        event: string;
        data: {
            canvasId: string;
        };
    } | undefined>;
    broadcastToAll(event: string, data: any): void;
    broadcastToConversation(conversationId: string, event: string, data: any): void;
}
